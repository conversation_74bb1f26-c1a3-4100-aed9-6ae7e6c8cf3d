[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[project]
name = "pwnagotchi"
dynamic = ["version"]
dependencies = [
    "PyYAML", "dbus-python", "file-read-backwards", "flask", "flask-cors",
    "flask-wtf", "gast", "gpiozero", "inky", "numpy", "pycryptodome", "pydrive2", "python-dateutil",
    "requests", "rpi-lgpio", "rpi_hardware_pwm", "scapy", "setuptools", "shimmy", "smbus", "smbus2",
    "spidev", "tomlkit", "toml", "tweepy", "websockets", "pisugar",
]

requires-python = ">=3.11"
authors = [
  {name = "Evilsocket", email = "<EMAIL>"},
  {name = "Jayofelony", email = "<EMAIL>"}
]
maintainers = [
  {name = "Jayofelony", email = "<EMAIL>"}
]
description = "(⌐■_■) - Deep Reinforcement Learning instrumenting bettercap for WiFI pwning."
readme = "README.md"
license = {file = "LICENSE.md"}
classifiers = [
    'Programming Language :: Python :: 3.11',
    'Development Status :: 5 - Production/Stable',
    'License :: OSI Approved :: GNU General Public License (GPL)',
    'Environment :: Console',
]

[tool.setuptools.dynamic]
version = {attr = "pwnagotchi.__version__"}

[project.urls]
Homepage = "https://pwnagotchi.org/"
Documentation = "https://github.com/jayofelony/pwnagotchi/wiki"
Repository = "https://github.com/jayofelony/pwnagotchi.git"
GitHub = "https://github.com/jayofelony/pwnagotchi"
Issues = "https://github.com/jayofelony/pwnagotchi/issues"
Download = "https://github.com/jayofelony/pwnagotchi/releases/latest"

[project.scripts]
pwnagotchi = "pwnagotchi.cli:pwnagotchi_cli"