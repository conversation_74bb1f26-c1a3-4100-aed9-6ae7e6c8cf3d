<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="24">
            <item index="0" class="java.lang.String" itemvalue="tweepy" />
            <item index="1" class="java.lang.String" itemvalue="PyYAML" />
            <item index="2" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="3" class="java.lang.String" itemvalue="file-read-backwards" />
            <item index="4" class="java.lang.String" itemvalue="requests" />
            <item index="5" class="java.lang.String" itemvalue="gast" />
            <item index="6" class="java.lang.String" itemvalue="torchvision" />
            <item index="7" class="java.lang.String" itemvalue="shimmy" />
            <item index="8" class="java.lang.String" itemvalue="flask-cors" />
            <item index="9" class="java.lang.String" itemvalue="scapy" />
            <item index="10" class="java.lang.String" itemvalue="RPi.GPIO" />
            <item index="11" class="java.lang.String" itemvalue="websockets" />
            <item index="12" class="java.lang.String" itemvalue="inky" />
            <item index="13" class="java.lang.String" itemvalue="smbus2" />
            <item index="14" class="java.lang.String" itemvalue="stable-baselines3" />
            <item index="15" class="java.lang.String" itemvalue="torch" />
            <item index="16" class="java.lang.String" itemvalue="toml" />
            <item index="17" class="java.lang.String" itemvalue="spidev" />
            <item index="18" class="java.lang.String" itemvalue="flask-wtf" />
            <item index="19" class="java.lang.String" itemvalue="gym" />
            <item index="20" class="java.lang.String" itemvalue="dbus-python" />
            <item index="21" class="java.lang.String" itemvalue="pycryptodome" />
            <item index="22" class="java.lang.String" itemvalue="flask" />
            <item index="23" class="java.lang.String" itemvalue="Pillow" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>