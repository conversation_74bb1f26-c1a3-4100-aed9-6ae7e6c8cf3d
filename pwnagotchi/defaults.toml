[main]
name = "pwnagotchi"
lang = "en"
iface = "wlan0mon"
mon_start_cmd = "/usr/bin/monstart"
mon_stop_cmd = "/usr/bin/monstop"
mon_max_blind_epochs = 5
no_restart = false
whitelist = [
    "EXAMPLE_NETWORK",
    "ANOTHER_EXAMPLE_NETWORK",
    "fo:od:ba:be:fo:od",
    "fo:od:ba"
]
confd = "/etc/pwnagotchi/conf.d/"
custom_plugin_repos = [
    "https://github.com/jayofelony/pwnagotchi-torch-plugins/archive/master.zip",
    "https://github.com/Sniffleupagus/pwnagotchi_plugins/archive/master.zip",
    "https://github.com/NeonLightning/pwny/archive/master.zip",
    "https://github.com/marbasec/UPSLite_Plugin_1_3/archive/master.zip",
    "https://github.com/wpa-2/Pwnagotchi-Plugins/archive/master.zip",
    "https://github.com/cyberartemio/wardriver-pwnagotchi-plugin/archive/main.zip"
]
custom_plugins = "/usr/local/share/pwnagotchi/custom-plugins/"

[main.plugins.auto-tune]
enabled = true

[main.plugins.auto_backup]
enabled = false
interval = "daily"    # or "hourly", or a number (minutes)
max_tries = 0
backup_location = "/home/<USER>/"
files = [
  "/root/settings.yaml",
  "/root/client_secrets.json",
  "/root/.api-report.json",
  "/root/.ssh",
  "/root/.bashrc",
  "/root/.profile",
  "/home/<USER>/handshakes",
  "/root/peers",
  "/etc/pwnagotchi/",
  "/usr/local/share/pwnagotchi/custom-plugins",
  "/etc/ssh/",
  "/home/<USER>/.bashrc",
  "/home/<USER>/.profile",
  "/home/<USER>/.wpa_sec_uploads"
]
exclude = [ "/etc/pwnagotchi/logs/*"]
commands = [ "tar cf {backup_file} {files}"]

[main.plugins.auto-update]
enabled = true
install = true
interval = 1
token = "" # Create a personal access token (classic) with scope set to public_repo to use the GitHub API

[main.plugins.bt-tether]
enabled = false
phone-name = "" # name as shown on the phone i.e. "Pwnagotchi's Phone"
mac = ""
phone = "" # android or ios
ip = "" # optional, default : ************ if android or *********** if ios
dns = "******* *******" # optional, default (google): "******* *******". Consider using anonymous DNS like OpenNic :-)

[main.plugins.fix_services]
enabled = true

[main.plugins.cache]
enabled = true

[main.plugins.gdrivesync]
enabled = false
backupfiles = [""]
backup_folder = "PwnagotchiBackups"
interval = 1

[main.plugins.gpio_buttons]
enabled = false

[main.plugins.gps]
enabled = false
speed = 19200
device = "/dev/ttyUSB0" # for GPSD: "localhost:2947"

[main.plugins.gps_listener]
enabled = false

[main.plugins.grid]
enabled = true
report = true

[main.plugins.logtail]
enabled = false
max-lines = 10000

[main.plugins.memtemp]
enabled = false
scale = "celsius"
orientation = "horizontal"

[main.plugins.ohcapi]
enabled = false
api_key = "sk_your_api_key_here"
receive_email = "yes"

[main.plugins.pwndroid]
enabled = false
display = false # show coords on display
display_altitude = false # show altitude on display

[main.plugins.pisugarx]
enabled = false
rotation = false
default_display = "percentage"
lowpower_shutdown = true
lowpower_shutdown_level = 10 # battery percent at which the device will turn off
max_charge_voltage_protection = false #It will limit the battery voltage to about 80% to extend battery life

[main.plugins.pwncrack]
enabled = false
key = ""

[main.plugins.session-stats]
enabled = false
save_directory = "/var/tmp/pwnagotchi/sessions/"

[main.plugins.ups_hat_c]
enabled = false
label_on = true  # show BAT label or just percentage
shutdown = 5  # battery percent at which the device will turn off
bat_x_coord = 140
bat_y_coord = 0

[main.plugins.ups_lite]
enabled = false
shutdown = 2

[main.plugins.webcfg]
enabled = true

[main.plugins.webgpsmap]
enabled = false

[main.plugins.wigle]
enabled = false
api_key = "" # mandatory
cvs_dir = "/tmp" # optionnal, is set, the CVS is written to this directory
donate = false # default: off
timeout = 30 # default: 30
position = [7, 85] # optionnal

[main.plugins.wpa-sec]
enabled = false
api_key = ""
api_url = "https://wpa-sec.stanev.org"
download_results = false
show_pwd = false

[main.log]
path = "/etc/pwnagotchi/log/pwnagotchi.log"
path-debug = "/etc/pwnagotchi/log/pwnagotchi-debug.log"

[main.log.rotation]
enabled = true
size = "10M"

[personality]
advertise = true
deauth = true
associate = true
channels = []
min_rssi = -200
ap_ttl = 120
sta_ttl = 300
recon_time = 30
max_inactive_scale = 2
recon_inactive_multiplier = 2
hop_recon_time = 10
min_recon_time = 5
max_interactions = 3
max_misses_for_recon = 5
excited_num_epochs = 10
bored_num_epochs = 15
sad_num_epochs = 25
bond_encounters_factor = 20000
throttle_a = 0.4
throttle_d = 0.9

[ui]
invert = false # false = black background, true = white background
cursor = true
fps = 0.0

[ui.font]
name = "DejaVuSansMono" # for japanese: fonts-japanese-gothic
size_offset = 0 # will be added to the font size

[ui.faces]
look_r = "( ⚆_⚆)"
look_l = "(☉_☉ )"
look_r_happy = "( ◕‿◕)"
look_l_happy = "(◕‿◕ )"
sleep = "(⇀‿‿↼)"
sleep2 = "(≖‿‿≖)"
awake = "(◕‿‿◕)"
bored = "(-__-)"
intense = "(°▃▃°)"
cool = "(⌐■_■)"
happy = "(•‿‿•)"
excited = "(ᵔ◡◡ᵔ)"
grateful = "(^‿‿^)"
motivated = "(☼‿‿☼)"
demotivated = "(≖__≖)"
smart = "(✜‿‿✜)"
lonely = "(ب__ب)"
sad = "(╥☁╥ )"
angry = "(-_-')"
friend = "(♥‿‿♥)"
broken = "(☓‿‿☓)"
debug = "(#__#)"
upload = "(1__0)"
upload1 = "(1__1)"
upload2 = "(0__1)"
png = false
position_x = 0
position_y = 34

[ui.web]
enabled = true
address = "::" # listening on both ipv4 and ipv6 - switch to 0.0.0.0 to listen on just ipv4
auth = false
username = "changeme" # if auth is true
password = "changeme" # if auth is true
origin = ""
port = 8080
on_frame = ""

[ui.display]
enabled = false
rotation = 180
type = "waveshare_4"

[bettercap]
handshakes = "/home/<USER>/handshakes"
silence = [
    "ble.device.new",
    "ble.device.lost",
    "ble.device.service.discovered",
    "ble.device.characteristic.discovered",
    "ble.device.disconnected",
    "ble.device.connected",
    "ble.connection.timeout",
    "wifi.client.new",
    "wifi.client.lost",
    "wifi.client.probe",
    "wifi.ap.new",
    "wifi.ap.lost",
    "mod.started"
]

[fs.memory]
enabled = true

[fs.memory.mounts.log]
enabled = true
mount = "/etc/pwnagotchi/log/"
size = "50M"
sync = 60
zram = true
rsync = true

[fs.memory.mounts.data]
enabled = true
mount = "/var/tmp/pwnagotchi"
size = "10M"
sync = 3600
zram = true
rsync = true
