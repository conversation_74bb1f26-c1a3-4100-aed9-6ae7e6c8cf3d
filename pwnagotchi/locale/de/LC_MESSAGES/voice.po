# German language
# Copyright (C) 2019
# This file is distributed under the same license as the pwnagotchi package.
# <AUTHOR> <EMAIL>, 2019.
#
msgid ""
msgstr ""
"Project-Id-Version: 0.0.1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 20:46+0100\n"
"PO-Revision-Date: 2019-09-29 14:00+0200\n"
"Last-Translator: dadav <<EMAIL>>\n"
"Language-Team: DE <<EMAIL>>\n"
"Language: German\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"

msgid "ZzzzZZzzzzZzzz"
msgstr ""

msgid "Hi, I'm Pwnagotchi! Starting ..."
msgstr "Hi, ich bin ein Pwnagotchi! Starte..."

msgid "New day, new hunt, new pwns!"
msgstr "Neuer Tag, neue Jagd, neue Pwns!"

msgid "Hack the Planet!"
msgstr "Hack den Planeten!"

msgid "No more mister Wi-Fi!!"
msgstr ""

msgid "Pretty fly 4 a Wi-Fi!"
msgstr ""

msgid "Generating keys, do not turn off ..."
msgstr "Generiere Schlüssel, nicht ausschalten..."

#, python-brace-format
msgid "Hey, channel {channel} is free! Your AP will say thanks."
msgstr "Hey, Channel {channel} ist frei! Dein AP wird es Dir danken."

msgid "Reading last session logs ..."
msgstr "Lese die Logs der letzten Session..."

#, python-brace-format
msgid "Read {lines_so_far} log lines so far ..."
msgstr "Bisher {lines_so_far} Zeilen im Log gelesen..."

msgid "I'm bored ..."
msgstr "Mir ist langweilig..."

msgid "Let's go for a walk!"
msgstr "Lass uns spazieren gehen!"

msgid "This is the best day of my life!"
msgstr "Das ist der beste Tag meines Lebens!"

msgid "Shitty day :/"
msgstr "Scheißtag :/"

msgid "I'm extremely bored ..."
msgstr "Mir ist sau langweilig..."

msgid "I'm very sad ..."
msgstr "Ich bin sehr traurig..."

msgid "I'm sad"
msgstr "Ich bin traurig"

msgid "Leave me alone ..."
msgstr "Lass mich in ruhe ..."

msgid "I'm mad at you!"
msgstr "Ich bin sauer auf Dich!"

msgid "I'm living the life!"
msgstr "Ich lebe das Leben!"

msgid "I pwn therefore I am."
msgstr "Ich pwne, also bin ich."

msgid "So many networks!!!"
msgstr "So viele Netzwerke!!!"

msgid "I'm having so much fun!"
msgstr "Ich habe sooo viel Spaß!"

msgid "My crime is that of curiosity ..."
msgstr "Mein Verbrechen ist das der Neugier..."

#, python-brace-format
msgid "Hello {name}! Nice to meet you."
msgstr "Hallo {name}, schön Dich kennenzulernen."

#, python-brace-format
msgid "Yo {name}! Sup?"
msgstr "Jo {name}! Was geht!?"

#, python-brace-format
msgid "Hey {name} how are you doing?"
msgstr "Hey {name}, wie geht's?"

#, python-brace-format
msgid "Unit {name} is nearby!"
msgstr "Gerät {name} ist in der Nähe!"

#, python-brace-format
msgid "Uhm ... goodbye {name}"
msgstr "Uhm... tschüß {name}"

#, python-brace-format
msgid "{name} is gone ..."
msgstr "{name} ist weg..."

#, python-brace-format
msgid "Whoops ... {name} is gone."
msgstr "Whoops... {name} ist weg."

#, python-brace-format
msgid "{name} missed!"
msgstr "{name} verpasst!"

msgid "Missed!"
msgstr "Verpasst!"

msgid "Good friends are a blessing!"
msgstr "Gute Freunde sind ein Segen!"

msgid "I love my friends!"
msgstr "Ich liebe meine Freunde!"

msgid "Nobody wants to play with me ..."
msgstr "Niemand will mit mir spielen..."

msgid "I feel so alone ..."
msgstr "Ich fühl' mich so allein..."

msgid "Where's everybody?!"
msgstr "Wo sind denn alle?!"

#, python-brace-format
msgid "Napping for {secs}s ..."
msgstr "Schlafe für {secs}s..."

msgid "Zzzzz"
msgstr ""

#, python-brace-format
msgid "ZzzZzzz ({secs}s)"
msgstr ""

msgid "Good night."
msgstr "Gute Nacht."

msgid "Zzz"
msgstr ""

#, python-brace-format
msgid "Waiting for {secs}s ..."
msgstr "Warte für {secs}s..."

#, python-brace-format
msgid "Looking around ({secs}s)"
msgstr "Schaue mich um ({secs}s)"

#, python-brace-format
msgid "Hey {what} let's be friends!"
msgstr "Hey {what}, lass uns Freunde sein!"

#, python-brace-format
msgid "Associating to {what}"
msgstr "Verbinde mit {what}"

#, python-brace-format
msgid "Yo {what}!"
msgstr "Jo {what}!"

#, python-brace-format
msgid "Just decided that {mac} needs no WiFi!"
msgstr "Ich denke, dass {mac} kein WiFi braucht!"

#, python-brace-format
msgid "Deauthenticating {mac}"
msgstr "Deauthentifiziere {mac}"

#, python-brace-format
msgid "Kickbanning {mac}!"
msgstr "Kicke {mac}!"

#, python-brace-format
msgid "Cool, we got {num} new handshake{plural}!"
msgstr "Cool, wir haben {num} neue Handshake{plural}!"

#, python-brace-format
msgid "You have {count} new message{plural}!"
msgstr "Cool, wir haben {num} neue Handshake{plural}!"

msgid "Oops, something went wrong ... Rebooting ..."
msgstr "Ops, da ist was schief gelaufen... Starte neu..."

#, python-brace-format
msgid "Uploading data to {to} ..."
msgstr ""

#, python-brace-format
msgid "Downloading from {name} ..."
msgstr "Schlafe für {name} ..."

#, python-brace-format
msgid "Kicked {num} stations\n"
msgstr "{num} Stationen gekickt\n"

msgid "Made >999 new friends\n"
msgstr ">999 neue Freunde gefunden\n"

#, python-brace-format
msgid "Made {num} new friends\n"
msgstr "{num} neue Freunde gefunden\n"

#, python-brace-format
msgid "Got {num} handshakes\n"
msgstr "{num} Handshakes aufgez.\n"

msgid "Met 1 peer"
msgstr "1 Peer getroffen."

#, python-brace-format
msgid "Met {num} peers"
msgstr "{num} Peers getroffen"

#, python-brace-format
msgid ""
"I've been pwning for {duration} and kicked {deauthed} clients! I've also met "
"{associated} new friends and ate {handshakes} handshakes! #pwnagotchi "
"#pwnlog #pwnlife #hacktheplanet #skynet"
msgstr ""
"Ich war {duration} am Pwnen und habe {deauthed} Clients gekickt! Außerdem "
"habe ich {associated} neue Freunde getroffen und {handshakes} Handshakes "
"gefressen! #pwnagotchi #pwnlog #pwnlife #hacktheplanet #skynet"

msgid "hours"
msgstr "Stunden"

msgid "minutes"
msgstr "Minuten"

msgid "seconds"
msgstr "Sekunden"

msgid "hour"
msgstr "Stunde"

msgid "minute"
msgstr "Minute"

msgid "second"
msgstr "Sekunde"

#~ msgid "AI ready."
#~ msgstr "KI bereit."

#~ msgid "The neural network is ready."
#~ msgstr "Das neurale Netz ist bereit."
