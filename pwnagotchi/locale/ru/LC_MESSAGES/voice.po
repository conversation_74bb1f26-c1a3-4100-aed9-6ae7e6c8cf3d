# Pwnagotchi Russian translation.
# Copyright (C) 2019
# This file is distributed under the same license as the pwnagotchi package.
# <AUTHOR> <EMAIL>, 2019.
# Second author <https://github.com/mbgroot>, 2019
msgid ""
msgstr ""
"Project-Id-Version: Pwnagotchi Russian translation v 0.0.2\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 20:46+0100\n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: Russian\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"X-Generator: Poedit 2.2.4\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-Basepath: .\n"
"X-Poedit-SearchPath-0: voice.po\n"

msgid "ZzzzZZzzzzZzzz"
msgstr "Хрррр..."

msgid "Hi, I'm Pwnagotchi! Starting ..."
msgstr "Привет, я Pwnagotchi! Стартуем!"

msgid "New day, new hunt, new pwns!"
msgstr "Новый день, новая охота, новые взломы!"

msgid "Hack the Planet!"
msgstr "Взломай эту Планету!"

msgid "No more mister Wi-Fi!!"
msgstr ""

msgid "Pretty fly 4 a Wi-Fi!"
msgstr ""

msgid "Generating keys, do not turn off ..."
msgstr "Генерация ключей, не выключайте..."

#, python-brace-format
msgid "Hey, channel {channel} is free! Your AP will say thanks."
msgstr "Эй, канал {channel} свободен! Ваша точка доступа скажет спасибо."

msgid "Reading last session logs ..."
msgstr "Чтение логов последнего сеанса..."

#, python-brace-format
msgid "Read {lines_so_far} log lines so far ..."
msgstr "Чтение {lines_so_far} строк журнала..."

msgid "I'm bored ..."
msgstr "Мне скучно …"

msgid "Let's go for a walk!"
msgstr "Пойдем прогуляемся!"

msgid "This is the best day of my life!"
msgstr "Лучший день в моей жизни!"

msgid "Shitty day :/"
msgstr "Дерьмовый день :/"

msgid "I'm extremely bored ..."
msgstr "Мне очень скучно …"

msgid "I'm very sad ..."
msgstr "Мне очень грустно …"

msgid "I'm sad"
msgstr "Мне грустно"

msgid "Leave me alone ..."
msgstr "Оставь меня в покое..."

msgid "I'm mad at you!"
msgstr "Я зол на тебя!"

msgid "I'm living the life!"
msgstr "Живу полной жизнью!"

msgid "I pwn therefore I am."
msgstr "Я взламываю, поэтому я существую."

msgid "So many networks!!!"
msgstr "Так много сетей!!!"

msgid "I'm having so much fun!"
msgstr "Мне так весело!"

msgid "My crime is that of curiosity ..."
msgstr "Моё преступление - это любопытство…"

#, python-brace-format
msgid "Hello {name}! Nice to meet you."
msgstr "Привет, {name}! Рад встрече с тобой!"

#, python-brace-format
msgid "Yo {name}! Sup?"
msgstr ""

#, python-brace-format
msgid "Hey {name} how are you doing?"
msgstr "Хэй {name}! Как дела?"

#, python-brace-format
msgid "Unit {name} is nearby!"
msgstr "Цель {name} рядом!"

#, python-brace-format
msgid "Uhm ... goodbye {name}"
msgstr "Хм … до свидания {name}"

#, python-brace-format
msgid "{name} is gone ..."
msgstr "{name} ушла…"

#, python-brace-format
msgid "Whoops ... {name} is gone."
msgstr "Упс… {name} исчезла."

#, python-brace-format
msgid "{name} missed!"
msgstr "{name} упустил!"

msgid "Missed!"
msgstr "Промахнулся!"

msgid "Good friends are a blessing!"
msgstr "Хорошие друзья - это благословение!"

msgid "I love my friends!"
msgstr "Я люблю своих друзей!"

msgid "Nobody wants to play with me ..."
msgstr "Никто не хочет со мной играть ..."

msgid "I feel so alone ..."
msgstr "Я так одинок…"

msgid "Where's everybody?!"
msgstr "Где все?!"

#, python-brace-format
msgid "Napping for {secs}s ..."
msgstr "Дремлет {secs}с …"

msgid "Zzzzz"
msgstr "Хррр..."

#, python-brace-format
msgid "ZzzZzzz ({secs}s)"
msgstr "Хррррр.. ({secs}c)"

msgid "Good night."
msgstr "Доброй ночи."

msgid "Zzz"
msgstr "Хрррр"

#, python-brace-format
msgid "Waiting for {secs}s ..."
msgstr "Ждем {secs}c …"

#, python-brace-format
msgid "Looking around ({secs}s)"
msgstr "Осматриваюсь вокруг ({secs}с)"

#, python-brace-format
msgid "Hey {what} let's be friends!"
msgstr "Эй, {what} давай дружить!"

#, python-brace-format
msgid "Associating to {what}"
msgstr "Связываюсь с {what}"

#, python-brace-format
msgid "Yo {what}!"
msgstr "Йоy {what}!"

#, python-brace-format
msgid "Just decided that {mac} needs no WiFi!"
msgstr "Просто решил, что {mac} не нужен WiFi! Кхе-кхе)"

#, python-brace-format
msgid "Deauthenticating {mac}"
msgstr "Деаутентификация {mac}"

#, python-brace-format
msgid "Kickbanning {mac}!"
msgstr "Кикаю {mac}!"

#, python-brace-format
msgid "Cool, we got {num} new handshake{plural}!"
msgstr "Круто, мы получили {num} новое рукопожатие!"

#, python-brace-format
msgid "You have {count} new message{plural}!"
msgstr ""

msgid "Oops, something went wrong ... Rebooting ..."
msgstr "Ой, что-то пошло не так … Перезагружаюсь …"

#, python-brace-format
msgid "Uploading data to {to} ..."
msgstr ""

#, python-brace-format
msgid "Downloading from {name} ..."
msgstr ""

#, python-brace-format
msgid "Kicked {num} stations\n"
msgstr "Кикнул {num} станцию\n"

msgid "Made >999 new friends\n"
msgstr "Заимел >999 новых друзей\n"

#, python-brace-format
msgid "Made {num} new friends\n"
msgstr "Заимел {num} новых друзей\n"

#, python-brace-format
msgid "Got {num} handshakes\n"
msgstr "Получил {num} рукопожатий\n"

msgid "Met 1 peer"
msgstr "Встретился один знакомый"

#, python-brace-format
msgid "Met {num} peers"
msgstr "Встретились {num} приятелей"

#, python-brace-format
msgid ""
"I've been pwning for {duration} and kicked {deauthed} clients! I've also met "
"{associated} new friends and ate {handshakes} handshakes! #pwnagotchi "
"#pwnlog #pwnlife #hacktheplanet #skynet"
msgstr ""
"Я взламывал {duration} и кикнул {deauthed} клиентов! Я также встретил "
"{associated} новых друзей и съел {handshakes} рукопожатий! #pwnagotchi "
"#pwnlog #pwnlife #hacktheplanet #skynet"

msgid "hours"
msgstr "часов"

msgid "minutes"
msgstr "минут"

msgid "seconds"
msgstr ""

msgid "hour"
msgstr "час"

msgid "minute"
msgstr "минуту"

msgid "second"
msgstr ""

#~ msgid "AI ready."
#~ msgstr "A.I. готов."

#~ msgid "The neural network is ready."
#~ msgstr "Нейронная сеть готова."

#, python-brace-format
#~ msgid "Unit {name} is nearby! {name}"
#~ msgstr "Цель {name} близко!"
