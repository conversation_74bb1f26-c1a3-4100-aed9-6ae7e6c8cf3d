# pwnaigotchi voice data
# Copyright (C) 2019
# This file is distributed under the same license as the pwnagotchi package.
# <AUTHOR> <EMAIL>, 2019.
#
msgid ""
msgstr ""
"Project-Id-Version: 0.0.1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 20:46+0100\n"
"PO-Revision-Date: 2019-10-02 17:20+0000\n"
"Language-Team: pwnagotchi <<EMAIL>>\n"
"Language: Italian\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"

msgid "ZzzzZZzzzzZzzz"
msgstr "ZzzzZZzzzzZzzz"

msgid "Hi, I'm Pwnagotch<PERSON>! Starting ..."
msgstr "Ciao! Piacere Pwnagotchi! Caricamento ..."

msgid "New day, new hunt, new pwns!"
msgstr "Nuovo giorno...nuovi handshakes!!!"

msgid "Hack the Planet!"
msgstr "Hack il Pianeta"

msgid "No more mister Wi-Fi!!"
msgstr ""

msgid "Pretty fly 4 a Wi-Fi!"
msgstr ""

msgid "Generating keys, do not turn off ..."
msgstr "Generazione di chiavi, non spegnere"

#, python-brace-format
msgid "Hey, channel {channel} is free! Your AP will say thanks."
msgstr "Hey, il canale {channel} è libero! Il tuo AP ringrazia."

msgid "Reading last session logs ..."
msgstr "Lettura dei log dell'ultima sessione ..."

#, python-brace-format
msgid "Read {lines_so_far} log lines so far ..."
msgstr "Leggi le righe di log {lines_so_far} finora ..."

msgid "I'm bored ..."
msgstr "Che noia ..."

msgid "Let's go for a walk!"
msgstr "Andiamo a fare una passeggiata!"

msgid "This is the best day of my life!"
msgstr "Questo e il miglior giorno della mia vita!!!!"

msgid "Shitty day :/"
msgstr "Giorno di merda :/"

msgid "I'm extremely bored ..."
msgstr "Sono estremamente annoiato ..."

msgid "I'm very sad ..."
msgstr "Sono molto triste..."

msgid "I'm sad"
msgstr "Sono triste"

msgid "Leave me alone ..."
msgstr "Mi sento così solo..."

msgid "I'm mad at you!"
msgstr "sono arabiata con te"

msgid "I'm living the life!"
msgstr "sono viva la vita!"

msgid "I pwn therefore I am."
msgstr "Pwn ergo sum."

msgid "So many networks!!!"
msgstr "Qui pieno di reti!"

msgid "I'm having so much fun!"
msgstr "Mi sto divertendo tantissimo!"

msgid "My crime is that of curiosity ..."
msgstr "Il mio crimine ? quello della curiosit?"

#, python-brace-format
msgid "Hello {name}! Nice to meet you."
msgstr "Ciao {name}! E' un piacere."

#, python-brace-format
msgid "Yo {name}! Sup?"
msgstr "Yo {name} Come va"

#, python-brace-format
msgid "Hey {name} how are you doing?"
msgstr "Ehi {name} come stai?"

#, python-brace-format
msgid "Unit {name} is nearby!"
msgstr "L'Unit {name} e vicina!"

#, python-brace-format
msgid "Uhm ... goodbye {name}"
msgstr "Uhm ... addio {name}, mi mancherai..."

#, python-brace-format
msgid "{name} is gone ..."
msgstr "{name} se andato ..."

#, python-brace-format
msgid "Whoops ... {name} is gone."
msgstr "Whoops ...{name} se andato."

#, python-brace-format
msgid "{name} missed!"
msgstr "{name} scomparso..."

msgid "Missed!"
msgstr "Ehi! Dove sei andato!?"

msgid "Good friends are a blessing!"
msgstr "Buoni amici sono una benedizione"

msgid "I love my friends!"
msgstr "Amo i miei amici"

msgid "Nobody wants to play with me ..."
msgstr "Nessuno vuole giocare con me..."

msgid "I feel so alone ..."
msgstr "Mi sento cos solo..."

msgid "Where's everybody?!"
msgstr "Dove sono tutti?!"

#, python-brace-format
msgid "Napping for {secs}s ..."
msgstr "Schiaccio un pisolino per {secs}s ..."

msgid "Zzzzz"
msgstr "Zzzzz"

#, python-brace-format
msgid "ZzzZzzz ({secs}s)"
msgstr "ZzzZzzz ({secs}s)"

msgid "Good night."
msgstr "Buona notte"

msgid "Zzz"
msgstr "Zzz"

#, python-brace-format
msgid "Waiting for {secs}s ..."
msgstr "Aspetto {secs}s ..."

#, python-brace-format
msgid "Looking around ({secs}s)"
msgstr "Do uno sguardo qui intorno... ({secs}s)"

#, python-brace-format
msgid "Hey {what} let's be friends!"
msgstr "Hey {what}! Diventiamo amici!"

#, python-brace-format
msgid "Associating to {what}"
msgstr "Collegamento con {what} in corso..."

#, python-brace-format
msgid "Yo {what}!"
msgstr "Yo {what}!"

#, python-brace-format
msgid "Just decided that {mac} needs no WiFi!"
msgstr "Ho appena deciso che {mac} non necessita di WiFi!"

#, python-brace-format
msgid "Deauthenticating {mac}"
msgstr "Annullamento dell'autenticazione {mac}"

#, python-brace-format
msgid "Kickbanning {mac}!"
msgstr "Sto prendendo a calci {mac}!"

#, python-brace-format
msgid "Cool, we got {num} new handshake{plural}!"
msgstr "Bene, abbiamo {num} handshake{plural} in più!"

#, python-brace-format
msgid "You have {count} new message{plural}!"
msgstr "Bene, abbiamo {count} handshake{plural} in più!"

msgid "Oops, something went wrong ... Rebooting ..."
msgstr "Ops, qualcosa è andato storto ... Riavvio ..."

#, python-brace-format
msgid "Uploading data to {to} ..."
msgstr "Caricamento dei dati in {to}"

#, python-brace-format
msgid "Downloading from {name} ..."
msgstr "Scaricamento da {name} ..."

#, python-brace-format
msgid "Kicked {num} stations\n"
msgstr "{num} stazioni pestate\n"

msgid "Made >999 new friends\n"
msgstr ">999 nuovi amici\n"

#, python-brace-format
msgid "Made {num} new friends\n"
msgstr "{num} nuovi amici\n"

#, python-brace-format
msgid "Got {num} handshakes\n"
msgstr "{num} handshakes presi\n"

msgid "Met 1 peer"
msgstr "1 peer incontrato"

#, python-brace-format
msgid "Met {num} peers"
msgstr "{num} peers incontrati"

#, python-brace-format
msgid ""
"I've been pwning for {duration} and kicked {deauthed} clients! I've also met "
"{associated} new friends and ate {handshakes} handshakes! #pwnagotchi "
"#pwnlog #pwnlife #hacktheplanet #skynet"
msgstr ""
"Ho lavorato per {duration} e preso a calci {deauthed} clients! Ho anche "
"incontrato {associate} nuovi amici e ho mangiato {handshakes} handshakes! "
"#pwnagotchi #pwnlog #pwnlife #hacktheplanet #skynet"

msgid "hours"
msgstr "ore"

msgid "minutes"
msgstr "minuti"

msgid "seconds"
msgstr "secondi"

msgid "hour"
msgstr "ora"

msgid "minute"
msgstr "minuto"

msgid "second"
msgstr "secondo"

#~ msgid "AI ready."
#~ msgstr "IA pronta."

#~ msgid "The neural network is ready."
#~ msgstr "La rete neurale è pronta."
