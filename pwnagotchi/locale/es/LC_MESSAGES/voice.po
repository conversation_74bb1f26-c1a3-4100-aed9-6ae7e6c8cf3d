# pwnagotchi voice data
# Copyright (C) 2019
# This file is distributed under the same license as the pwnagotchi package.
# <AUTHOR> <EMAIL>, 2019.
#
msgid ""
msgstr ""
"Project-Id-Version: 0.0.1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 20:46+0100\n"
"PO-Revision-Date: 2020-08-25 23:06+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: Spanish, Castilian\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.4.1\n"

msgid "ZzzzZZzzzzZzzz"
msgstr "ZzzzZZzzzzZzzz"

msgid "Hi, I'm Pwnagotchi! Starting ..."
msgstr "¡Ho<PERSON>, soy Pwnagotchi! Empezando ..."

msgid "New day, new hunt, new pwns!"
msgstr "Nuevo día, nueva caceria, nuevos pwns!"

msgid "Hack the Planet!"
msgstr "¡Hackea el planeta!"

msgid "No more mister Wi-Fi!!"
msgstr ""

msgid "Pretty fly 4 a Wi-Fi!"
msgstr ""

msgid "Generating keys, do not turn off ..."
msgstr "Generando llaves, no me apagues ..."

#, python-brace-format
msgid "Hey, channel {channel} is free! Your AP will say thanks."
msgstr "¡Oye, el canal {channel} está libre! Tu AP lo agradecerá."

msgid "Reading last session logs ..."
msgstr "Leyendo los logs de la última sesión ..."

#, python-brace-format
msgid "Read {lines_so_far} log lines so far ..."
msgstr "Leyendo {lines_so_far} líneas de log hasta ahora ..."

msgid "I'm bored ..."
msgstr "Estoy aburrido ..."

msgid "Let's go for a walk!"
msgstr "¡Vamos por un paseo!"

msgid "This is the best day of my life!"
msgstr "¡Este es el mejor día de mi vida!"

msgid "Shitty day :/"
msgstr "Día de mierda :/"

msgid "I'm extremely bored ..."
msgstr "Estoy muy aburrido ..."

msgid "I'm very sad ..."
msgstr "Estoy muy triste ..."

msgid "I'm sad"
msgstr "Estoy triste"

msgid "Leave me alone ..."
msgstr "Me siento tan solo ..."

msgid "I'm mad at you!"
msgstr "Toy re enojado con vos!"

msgid "I'm living the life!"
msgstr "¡Estoy viviendo la vida!"

msgid "I pwn therefore I am."
msgstr "Pwneo, luego existo."

msgid "So many networks!!!"
msgstr "¡¡¡Cuántas redes!!!"

msgid "I'm having so much fun!"
msgstr "¡Me estoy divirtiendo mucho!"

msgid "My crime is that of curiosity ..."
msgstr "Mi crimen es la curiosidad ..."

#, python-brace-format
msgid "Hello {name}! Nice to meet you."
msgstr "¡Hola {name}! Encantado de conocerte."

#, python-brace-format
msgid "Yo {name}! Sup?"
msgstr "Que onda {name}!?"

#, python-brace-format
msgid "Hey {name} how are you doing?"
msgstr "Eh!, ¿Que haces {name}?"

#, python-brace-format
msgid "Unit {name} is nearby!"
msgstr "¡La unidad {name} está cerca!"

#, python-brace-format
msgid "Uhm ... goodbye {name}"
msgstr "Uhm ... adiós {name}"

#, python-brace-format
msgid "{name} is gone ..."
msgstr "{name} se fue ..."

#, python-brace-format
msgid "Whoops ... {name} is gone."
msgstr "Ups ... {name} se fue."

#, python-brace-format
msgid "{name} missed!"
msgstr "¡{name} perdido!"

msgid "Missed!"
msgstr "¡Perdido!"

msgid "Good friends are a blessing!"
msgstr "Lxs buenxs amigxs son una masa!"

msgid "I love my friends!"
msgstr "¡Amo a mis amigxs!"

msgid "Nobody wants to play with me ..."
msgstr "Nadie quiere jugar conmigo ..."

msgid "I feel so alone ..."
msgstr "Me siento tan solo ..."

msgid "Where's everybody?!"
msgstr "¡¿Dónde está todo el mundo?!"

#, python-brace-format
msgid "Napping for {secs}s ..."
msgstr "Descansando por {secs}s ..."

msgid "Zzzzz"
msgstr "Zzzzz"

#, python-brace-format
msgid "ZzzZzzz ({secs}s)"
msgstr "ZzzZzzz ({secs}s)"

msgid "Good night."
msgstr "Buenas noches."

msgid "Zzz"
msgstr "Zzz"

#, python-brace-format
msgid "Waiting for {secs}s ..."
msgstr "Esperando {secs}s .."

#, python-brace-format
msgid "Looking around ({secs}s)"
msgstr "Mirando alrededor ({secs}s)"

#, python-brace-format
msgid "Hey {what} let's be friends!"
msgstr "¡Oye {what} seamos amigos!"

#, python-brace-format
msgid "Associating to {what}"
msgstr "Asociándome a {what}"

#, python-brace-format
msgid "Yo {what}!"
msgstr "¡Ey {what}!"

#, python-brace-format
msgid "Just decided that {mac} needs no WiFi!"
msgstr "¡Acabo de decidir que {mac} no necesita WiFi!"

#, python-brace-format
msgid "Deauthenticating {mac}"
msgstr "Desautenticando a {mac}"

#, python-brace-format
msgid "Kickbanning {mac}!"
msgstr "¡Expulsando y baneando a {mac}!"

#, python-brace-format
msgid "Cool, we got {num} new handshake{plural}!"
msgstr "¡Genial, obtuvimos {num} nuevo{plural} handshake{plural}!"

#, python-brace-format
msgid "You have {count} new message{plural}!"
msgstr "¡Genial, obtuvimos {count} nuevo{plural} handshake{plural}!"

msgid "Oops, something went wrong ... Rebooting ..."
msgstr "Oops, algo salió mal ... Reiniciando ..."

#, python-brace-format
msgid "Uploading data to {to} ..."
msgstr "Subiendo data a {to} ..."

#, python-brace-format
msgid "Downloading from {name} ..."
msgstr "Descansando durante {name} ..."

#, python-brace-format
msgid "Kicked {num} stations\n"
msgstr "Expulsamos {num} estaciones\n"

msgid "Made >999 new friends\n"
msgstr "Hice >999 nuevos amigos\n"

#, python-brace-format
msgid "Made {num} new friends\n"
msgstr "Hice {num} nuevos amigos\n"

#, python-brace-format
msgid "Got {num} handshakes\n"
msgstr "Consegui {num} handshakes\n"

msgid "Met 1 peer"
msgstr "Conocí 1 colega"

#, python-brace-format
msgid "Met {num} peers"
msgstr "Conocí {num} colegas"

#, python-brace-format
msgid ""
"I've been pwning for {duration} and kicked {deauthed} clients! I've also met "
"{associated} new friends and ate {handshakes} handshakes! #pwnagotchi "
"#pwnlog #pwnlife #hacktheplanet #skynet"
msgstr ""
"¡He estado pwneando por {duration} y expulsé {deauthed} clientes! También "
"conocí {associated} nuevos amigos y comí {handshakes} handshakes! "
"#pwnagotchi #pwnlog #pwnlife #hacktheplanet #skynet"

msgid "hours"
msgstr "horas"

msgid "minutes"
msgstr "minutos"

msgid "seconds"
msgstr "segundos"

msgid "hour"
msgstr "hora"

msgid "minute"
msgstr "minuto"

msgid "second"
msgstr "segundo"

#~ msgid "AI ready."
#~ msgstr "IA lista."

#~ msgid "The neural network is ready."
#~ msgstr "La red neuronal está lista."
